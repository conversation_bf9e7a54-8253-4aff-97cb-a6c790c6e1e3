<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹窗层级测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="static/css/modern-components.css">
    <style>
        body {
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
        }
        
        .z-index-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .custom-input-wrapper {
            animation: slideInRight 0.3s ease-out;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">弹窗层级测试</h1>
        
        <div class="z-index-info">
            <strong>Z-Index 层级设置：</strong><br>
            • 现代UI弹窗 (图片查看): z-index: 1060<br>
            • Bootstrap下拉框: z-index: 1050<br>
            • 自定义属性弹窗: z-index: 1040<br>
        </div>
        
        <div class="test-section">
            <h5>1. 下拉框测试</h5>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    选择尺寸
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#">A4</a></li>
                    <li><a class="dropdown-item" href="#">A3</a></li>
                    <li><a class="dropdown-item" href="#">自定义</a></li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h5>2. 自定义属性弹窗测试</h5>
            <button class="btn btn-warning" onclick="showCustomAttributesModal()">
                <i class="fas fa-edit me-2"></i>显示自定义属性弹窗
            </button>
        </div>
        
        <div class="test-section">
            <h5>3. 图片查看弹窗测试</h5>
            <button class="btn btn-info" onclick="showImageModal()">
                <i class="fas fa-image me-2"></i>显示图片查看弹窗
            </button>
        </div>
        
        <div class="test-section">
            <h5>4. 自定义输入框测试</h5>
            <div class="spec-select-container" style="position: relative; z-index: 1050; display: flex; align-items: center; gap: 0.75rem;">
                <select class="form-select" onchange="toggleCustomInput(this)" style="max-width: 300px;">
                    <option value="">请选择尺寸</option>
                    <option value="A4">A4</option>
                    <option value="A3">A3</option>
                    <option value="custom">自定义</option>
                </select>

                <div class="custom-input-wrapper" id="testCustomInput"
                     style="display: none; position: absolute; top: 0; left: 320px; z-index: 1040; min-width: 200px;">
                    <div class="input-group" style="max-width: 200px;">
                        <input type="text" class="form-control custom-input-field"
                               placeholder="请输入自定义尺寸"
                               style="padding: 0.4rem 0.6rem; font-size: 0.85rem; max-width: 200px;">
                        <button type="button" class="btn btn-outline-secondary" onclick="clearTestCustomInput()"
                                style="padding: 0.4rem 0.6rem; font-size: 0.75rem;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h5>5. 层级测试</h5>
            <p class="text-muted">
                1. 先打开下拉框<br>
                2. 选择"自定义"选项，自定义输入框应该显示在下拉框右侧<br>
                3. 自定义输入框应该更小更紧凑<br>
                4. 然后点击"显示自定义属性弹窗"<br>
                5. 自定义弹窗应该在下拉框后面<br>
                6. 再点击"显示图片查看弹窗"<br>
                7. 图片弹窗应该在最前面
            </p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/modern-ui.js"></script>
    <script src="static/js/custom-attributes-display.js"></script>
    
    <script>
        function showCustomAttributesModal() {
            // 模拟自定义属性数据
            const mockData = {
                order_no: 'TEST001',
                custom_items: [
                    {
                        group_name: '尺寸',
                        custom_value: '210mm x 297mm',
                        price_modifier: 5.00
                    },
                    {
                        group_name: '页数',
                        custom_value: '120页',
                        price_modifier: 12.00
                    }
                ]
            };
            
            // 使用自定义属性显示类
            if (window.customAttributesDisplay) {
                window.customAttributesDisplay.showModal(mockData);
            } else {
                // 直接创建弹窗
                const modalHtml = `
                    <div class="modal fade custom-attributes-modal-backdrop" id="testCustomModal" tabindex="-1" style="z-index: 1040;">
                        <div class="modal-dialog modal-md">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">
                                        <i class="fas fa-edit me-2"></i>自定义属性详情
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <strong>订单号：</strong>TEST001
                                    </div>
                                    <div class="custom-item mb-3">
                                        <strong>尺寸：</strong>210mm x 297mm
                                        <span class="badge bg-success ms-2">+¥5.00</span>
                                    </div>
                                    <div class="custom-item mb-3">
                                        <strong>页数：</strong>120页
                                        <span class="badge bg-success ms-2">+¥12.00</span>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                // 移除已存在的弹窗
                const existing = document.getElementById('testCustomModal');
                if (existing) existing.remove();
                
                // 添加新弹窗
                document.body.insertAdjacentHTML('beforeend', modalHtml);
                
                // 显示弹窗
                const modal = new bootstrap.Modal(document.getElementById('testCustomModal'));
                modal.show();
            }
        }
        
        function showImageModal() {
            if (window.ModernUI && window.ModernUI.modal) {
                const modal = window.ModernUI.modal.create({
                    title: '商品大图',
                    content: `
                        <div class="text-center">
                            <img src="https://via.placeholder.com/600x400/667eea/white?text=测试图片"
                                 class="img-fluid rounded" alt="测试图片">
                        </div>
                    `,
                    closable: true,
                    showFooter: false,
                    size: 'lg'
                });

                window.ModernUI.modal.open(modal);
            } else {
                alert('ModernUI 模块未加载');
            }
        }

        function toggleCustomInput(selectElement) {
            const customInput = document.getElementById('testCustomInput');

            if (selectElement.value === 'custom') {
                customInput.style.display = 'block';
                const input = customInput.querySelector('input');
                if (input) {
                    setTimeout(() => input.focus(), 100);
                }
            } else {
                customInput.style.display = 'none';
                const input = customInput.querySelector('input');
                if (input) {
                    input.value = '';
                }
            }
        }

        function clearTestCustomInput() {
            const customInput = document.getElementById('testCustomInput');
            const select = document.querySelector('.spec-select-container select');
            const input = customInput.querySelector('input');

            if (input) {
                input.value = '';
            }
            customInput.style.display = 'none';
            if (select) {
                select.value = '';
            }
        }
    </script>
</body>
</html>
