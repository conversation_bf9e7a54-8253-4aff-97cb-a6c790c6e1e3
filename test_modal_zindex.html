<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹窗层级测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="static/css/modern-components.css">
    <style>
        body {
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
        }
        
        .z-index-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .custom-input-wrapper {
            animation: slideInRight 0.3s ease-out;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">弹窗层级测试</h1>
        
        <div class="z-index-info">
            <strong>布局和层级设置：</strong><br>
            • 下拉框尺寸: 缩小到160px宽度，非常紧凑<br>
            • 自定义输入框: 显示在下拉框右侧(left: 180px)，更小更紧凑<br>
            • 现代UI弹窗 (图片查看): z-index: 1060<br>
            • Bootstrap下拉框: z-index: 1050<br>
            • 自定义属性弹窗: z-index: 1040<br>
        </div>
        
        <div class="test-section">
            <h5>1. 下拉框测试</h5>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    选择尺寸
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#">A4</a></li>
                    <li><a class="dropdown-item" href="#">A3</a></li>
                    <li><a class="dropdown-item" href="#">自定义</a></li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h5>2. 自定义属性弹窗测试</h5>
            <button class="btn btn-warning" onclick="showCustomAttributesModal()">
                <i class="fas fa-edit me-2"></i>显示自定义属性弹窗
            </button>
        </div>
        
        <div class="test-section">
            <h5>3. 图片查看弹窗测试</h5>
            <button class="btn btn-info" onclick="showImageModal()">
                <i class="fas fa-image me-2"></i>显示图片查看弹窗
            </button>
        </div>
        
        <div class="test-section">
            <h5>4. 自定义输入框测试</h5>
            <div class="spec-select-container" style="position: relative; z-index: 1050; display: flex; align-items: center; gap: 0.75rem;">
                <select class="form-select" onchange="toggleCustomInput(this)"
                        style="max-width: 160px; padding: 0.4rem 0.6rem; font-size: 0.85rem;">
                    <option value="">请选择尺寸</option>
                    <option value="A4">A4</option>
                    <option value="A3">A3</option>
                    <option value="custom">自定义</option>
                </select>

                <div class="custom-input-wrapper" id="testCustomInput"
                     style="display: none; position: absolute; top: 0; left: 180px; z-index: 1040; min-width: 200px;">
                    <div class="input-group" style="max-width: 200px;">
                        <input type="text" class="form-control custom-input-field"
                               placeholder="请输入自定义尺寸"
                               style="padding: 0.4rem 0.6rem; font-size: 0.85rem; max-width: 200px;">
                        <button type="button" class="btn btn-outline-secondary" onclick="clearTestCustomInput()"
                                style="padding: 0.4rem 0.6rem; font-size: 0.75rem;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h5>5. 数量选择测试</h5>
            <div class="quantity-section">
                <div class="quantity-label">
                    <i class="fas fa-calculator me-2"></i>总数量
                </div>
                <div class="quantity-controls">
                    <!-- 默认数量选项 -->
                    <div class="quantity-presets mb-2" style="display: flex; gap: 0.5rem; flex-wrap: wrap; margin-bottom: 0.75rem;">
                        <button type="button" class="btn btn-outline-primary btn-sm quantity-preset" data-quantity="50"
                                style="padding: 0.25rem 0.5rem; font-size: 0.75rem; min-width: 40px;">50</button>
                        <button type="button" class="btn btn-outline-primary btn-sm quantity-preset" data-quantity="100"
                                style="padding: 0.25rem 0.5rem; font-size: 0.75rem; min-width: 40px;">100</button>
                        <button type="button" class="btn btn-outline-primary btn-sm quantity-preset" data-quantity="200"
                                style="padding: 0.25rem 0.5rem; font-size: 0.75rem; min-width: 40px;">200</button>
                        <button type="button" class="btn btn-outline-primary btn-sm quantity-preset" data-quantity="500"
                                style="padding: 0.25rem 0.5rem; font-size: 0.75rem; min-width: 40px;">500</button>
                    </div>

                    <!-- 自定义数量输入 -->
                    <div class="quantity-selector" style="display: flex; align-items: center; background: rgba(255, 255, 255, 0.8); border: 2px solid rgba(102, 126, 234, 0.2); border-radius: 0.5rem; overflow: hidden;">
                        <button type="button" class="quantity-btn" data-action="decrease" style="background: #f8f9fa; border: none; width: 2.2rem; height: 2.2rem;">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="quantity-input" value="1" min="1" max="9999" id="testQuantityInput"
                               style="width: 60px; min-width: 50px; border: none; text-align: center; height: 2.2rem;">
                        <button type="button" class="quantity-btn" data-action="increase" style="background: #f8f9fa; border: none; width: 2.2rem; height: 2.2rem;">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>

                <!-- 小尺寸价格显示 -->
                <div class="price-section-small" style="background: rgba(102, 126, 234, 0.05); border-radius: 0.5rem; padding: 0.75rem; margin-top: 1rem; border: 1px solid rgba(102, 126, 234, 0.1);">
                    <div class="price-main-small" style="display: flex; align-items: baseline; gap: 0.25rem; margin-bottom: 0.25rem;">
                        <span class="price-symbol-small" style="font-size: 0.9rem; color: #667eea; font-weight: 600;">¥</span>
                        <span class="price-value-small" id="testPriceValue" style="font-size: 1.4rem; font-weight: 700; color: #667eea;">20.10000</span>
                    </div>
                    <div class="price-info-small" style="color: #6c757d; font-size: 0.75rem;">
                        <small>起价，最终价格根据规格和数量计算</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h5>6. 层级测试</h5>
            <p class="text-muted">
                1. 先打开下拉框<br>
                2. 选择"自定义"选项，自定义输入框应该显示在下拉框右侧<br>
                3. 自定义输入框应该更小更紧凑<br>
                4. 测试数量选择：点击预设数量按钮或手动输入<br>
                5. 价格应该显示在数量下方，更小更紧凑<br>
                6. 然后点击"显示自定义属性弹窗"<br>
                7. 自定义弹窗应该在下拉框后面<br>
                8. 再点击"显示图片查看弹窗"<br>
                9. 图片弹窗应该在最前面
            </p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/modern-ui.js"></script>
    <script src="static/js/custom-attributes-display.js"></script>
    
    <script>
        function showCustomAttributesModal() {
            // 模拟自定义属性数据
            const mockData = {
                order_no: 'TEST001',
                custom_items: [
                    {
                        group_name: '尺寸',
                        custom_value: '210mm x 297mm',
                        price_modifier: 5.00
                    },
                    {
                        group_name: '页数',
                        custom_value: '120页',
                        price_modifier: 12.00
                    }
                ]
            };
            
            // 使用自定义属性显示类
            if (window.customAttributesDisplay) {
                window.customAttributesDisplay.showModal(mockData);
            } else {
                // 直接创建弹窗
                const modalHtml = `
                    <div class="modal fade custom-attributes-modal-backdrop" id="testCustomModal" tabindex="-1" style="z-index: 1040;">
                        <div class="modal-dialog modal-md">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">
                                        <i class="fas fa-edit me-2"></i>自定义属性详情
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <strong>订单号：</strong>TEST001
                                    </div>
                                    <div class="custom-item mb-3">
                                        <strong>尺寸：</strong>210mm x 297mm
                                        <span class="badge bg-success ms-2">+¥5.00</span>
                                    </div>
                                    <div class="custom-item mb-3">
                                        <strong>页数：</strong>120页
                                        <span class="badge bg-success ms-2">+¥12.00</span>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                // 移除已存在的弹窗
                const existing = document.getElementById('testCustomModal');
                if (existing) existing.remove();
                
                // 添加新弹窗
                document.body.insertAdjacentHTML('beforeend', modalHtml);
                
                // 显示弹窗
                const modal = new bootstrap.Modal(document.getElementById('testCustomModal'));
                modal.show();
            }
        }
        
        function showImageModal() {
            if (window.ModernUI && window.ModernUI.modal) {
                const modal = window.ModernUI.modal.create({
                    title: '商品大图',
                    content: `
                        <div class="text-center">
                            <img src="https://via.placeholder.com/600x400/667eea/white?text=测试图片"
                                 class="img-fluid rounded" alt="测试图片">
                        </div>
                    `,
                    closable: true,
                    showFooter: false,
                    size: 'lg'
                });

                window.ModernUI.modal.open(modal);
            } else {
                alert('ModernUI 模块未加载');
            }
        }

        function toggleCustomInput(selectElement) {
            const customInput = document.getElementById('testCustomInput');

            if (selectElement.value === 'custom') {
                customInput.style.display = 'block';
                const input = customInput.querySelector('input');
                if (input) {
                    setTimeout(() => input.focus(), 100);
                }
            } else {
                customInput.style.display = 'none';
                const input = customInput.querySelector('input');
                if (input) {
                    input.value = '';
                }
            }
        }

        function clearTestCustomInput() {
            const customInput = document.getElementById('testCustomInput');
            const select = document.querySelector('.spec-select-container select');
            const input = customInput.querySelector('input');

            if (input) {
                input.value = '';
            }
            customInput.style.display = 'none';
            if (select) {
                select.value = '';
            }
        }

        // 数量选择功能
        function initTestQuantityControls() {
            const quantityInput = document.getElementById('testQuantityInput');
            const presetButtons = document.querySelectorAll('.quantity-preset');
            const decreaseBtn = document.querySelector('.quantity-btn[data-action="decrease"]');
            const increaseBtn = document.querySelector('.quantity-btn[data-action="increase"]');
            const priceValue = document.getElementById('testPriceValue');

            let currentQuantity = 1;
            const basePrice = 20.10000;

            // 更新价格显示
            function updatePrice() {
                const totalPrice = (basePrice * currentQuantity).toFixed(5);
                if (priceValue) {
                    priceValue.textContent = totalPrice;
                }
            }

            // 预设数量按钮
            presetButtons.forEach(button => {
                button.addEventListener('click', function() {
                    currentQuantity = parseInt(this.dataset.quantity);
                    quantityInput.value = currentQuantity;
                    updatePrice();

                    // 更新按钮状态
                    presetButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 减少按钮
            if (decreaseBtn) {
                decreaseBtn.addEventListener('click', function() {
                    if (currentQuantity > 1) {
                        currentQuantity--;
                        quantityInput.value = currentQuantity;
                        updatePrice();
                        presetButtons.forEach(btn => btn.classList.remove('active'));
                    }
                });
            }

            // 增加按钮
            if (increaseBtn) {
                increaseBtn.addEventListener('click', function() {
                    if (currentQuantity < 9999) {
                        currentQuantity++;
                        quantityInput.value = currentQuantity;
                        updatePrice();
                        presetButtons.forEach(btn => btn.classList.remove('active'));
                    }
                });
            }

            // 输入框变化
            if (quantityInput) {
                quantityInput.addEventListener('change', function() {
                    const value = parseInt(this.value);
                    if (value >= 1 && value <= 9999) {
                        currentQuantity = value;
                        updatePrice();
                    } else {
                        this.value = currentQuantity;
                    }
                    presetButtons.forEach(btn => btn.classList.remove('active'));
                });
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initTestQuantityControls();
        });
    </script>
</body>
</html>
